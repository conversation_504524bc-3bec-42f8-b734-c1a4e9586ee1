{"id": "c9663f16-8fa6-40fb-923f-a67094935813", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"categories_name_unique": {"name": "categories_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.games": {"name": "games", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "cover_image_url": {"name": "cover_image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "game_url": {"name": "game_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "play_count": {"name": "play_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"games_category_id_categories_id_fk": {"name": "games_category_id_categories_id_fk", "tableFrom": "games", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}