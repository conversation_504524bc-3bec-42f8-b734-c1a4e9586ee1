
'use client';

import { Navbar } from "@/components/Navbar";
import { getCategories } from '@/lib/data';

export function AppLayout({
  children,
  categories,
}: {
  children: React.ReactNode;
  categories: Awaited<ReturnType<typeof getCategories>>;
}) {

  return (
    <div className="min-h-screen bg-black text-white">
      <Navbar categories={categories} />
    
        <div className="w-full p-4">
          {children}
        </div>
        
      </div>

  );
}
