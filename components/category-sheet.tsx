'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Gamepad2, X, Home, MenuIcon } from 'lucide-react';
import { getCategories } from '@/lib/data';
import { Sheet, SheetContent, SheetTrigger, SheetClose } from './ui/sheet';

interface CategorySheetProps {
  categories: Awaited<ReturnType<typeof getCategories>>;
}

export function CategorySheet({categories}: CategorySheetProps) {
  const pathname = usePathname();

  return (
    <>
        <Sheet>
          <SheetTrigger className="p-2 hover:bg-gray-800 rounded-lg transition-colors">
            <MenuIcon className="w-6 h-6" />
            <span className="sr-only">Open menu</span>
          </SheetTrigger>
          <SheetContent side="left" className='bg-black text-white border-gray-800 w-[280px] sm:w-[320px]' overlayClassName='bg-black/90'>
            <div className="overflow-y-auto h-full pt-16">
              {/* Header */}
              <div className="px-4 pb-4 border-b border-gray-800">
                <h2 className="text-lg font-semibold text-purple-400">Navigation</h2>
              </div>

              <nav className="px-4 pt-4">
                <ul className="space-y-1">
                  {/* Home link */}
                  <li>
                    <SheetClose asChild>
                    <Link
                      href="/"
                      className={`flex items-center p-3 rounded-lg hover:bg-gray-700 transition-colors group ${
                        pathname === '/' ? 'bg-purple-600 hover:bg-purple-700' : ''
                      }`}
                    >
                      <Home className="w-5 h-5" />
                      <span className="font-medium ml-3 group-hover:text-purple-300">All Games</span>
                    </Link>
                    </SheetClose>
                  </li>

                  {/* Categories section */}
                  <li className="pt-4">
                    <div className="px-3 pb-2">
                      <h3 className="text-sm font-medium text-gray-400 uppercase tracking-wider">Categories</h3>
                    </div>
                  </li>

                  {/* Category links */}
                  {categories.map((category) => {
                    const isActive = pathname === `/category/${category.id}`;
                    return (
                      <li key={category.id}>
                        <SheetClose asChild>
                        <Link
                          href={`/category/${category.id}`}
                          className={`flex items-center p-3 group rounded-lg hover:bg-gray-700 transition-colors ${
                            isActive ? 'bg-purple-600 hover:bg-purple-700' : ''
                          }`}
                        >
                          <Gamepad2 className="w-5 h-5" />
                          <span className="font-medium ml-3 group-hover:text-purple-300">{category.name}</span>
                        </Link>
                        </SheetClose>
                      </li>
                    );
                  })}

                  {/* Additional links */}
                  <li className="pt-4">
                    <div className="px-3 pb-2">
                      <h3 className="text-sm font-medium text-gray-400 uppercase tracking-wider">Quick Links</h3>
                    </div>
                  </li>

                  <li>
                    <SheetClose asChild>
                    <Link
                      href="/hot"
                      className="flex items-center p-3 rounded-lg hover:bg-gray-700 transition-colors group"
                    >
                      <Gamepad2 className="w-5 h-5" />
                      <span className="font-medium ml-3 group-hover:text-purple-300">Hot Games</span>
                    </Link>
                    </SheetClose>
                  </li>

                  <li>
                    <SheetClose asChild>
                    <Link
                      href="/new"
                      className="flex items-center p-3 rounded-lg hover:bg-gray-700 transition-colors group"
                    >
                      <Gamepad2 className="w-5 h-5" />
                      <span className="font-medium ml-3 group-hover:text-purple-300">New Games</span>
                    </Link>
                    </SheetClose>
                  </li>
                </ul>
              </nav>
            </div>
        </SheetContent>

        </Sheet>
    </>
  );
}
