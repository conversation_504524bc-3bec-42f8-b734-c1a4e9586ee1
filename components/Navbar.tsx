
'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Heart, X } from 'lucide-react';
import { CategorySheet } from './category-sheet';
import { getCategories } from '@/lib/data';
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuList } from './ui/navigation-menu';
import { NavigationMenuTrigger } from '@radix-ui/react-navigation-menu';
import { NavigationMenuLink } from '@radix-ui/react-navigation-menu';

type Categories = Awaited<ReturnType<typeof getCategories>>;

export function Navbar({categories }: {categories: Categories }) {
  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);

  return (
    <header className="bg-black text-white shadow-md sticky top-0 z-40 border-b border-gray-800">
      <div className="mx-auto px-4 py-3 max-w-7xl">
        {/* Main navigation bar */}
        <div className="flex justify-between items-center">
          {/* Left side - Logo and mobile menu */}
          <div className="flex items-center gap-3">
            {/* Mobile menu button */}
            <div className="md:hidden">
              <CategorySheet categories={categories} />
            </div>

            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <Heart className="text-[#f005a5] w-6 h-6 sm:w-7 sm:h-7" />
              <span className="text-lg sm:text-xl font-bold">Love Tester</span>
            </Link>
          </div>

          {/* Desktop navigation menu */}
          <div className="hidden md:block">
            <NavigationMenu viewport={false}>
              <NavigationMenuList className="flex items-center space-x-6">
                <NavigationMenuItem>
                  <NavigationMenuTrigger className='cursor-pointer text-white hover:text-[#f005a5] transition-colors px-3 py-2 rounded-md hover:bg-gray-800'>
                    Categories
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="w-[460px]">
                      <div className="grid grid-cols-2 gap-3">
                        {categories.map((category) => (
                          <Link
                            key={category.id}
                            href={`/category/${category.id}`}
                            className="block p-3 text-[#f005a5] rounded-lg hover:bg-gray-500 transition-colors group"
                          >
                            <div className="font-medium text-white group-hover:text-[#f005a5]">{category.name}</div>
                            <div className="text-sm text-gray-400 mt-1">Explore {category.name.toLowerCase()} games</div>
                          </Link>
                        ))}
                      </div>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink className='cursor-pointer text-white hover:text-purple-400 transition-colors px-3 py-2 rounded-md hover:bg-gray-800' asChild>
                    <Link href="/hot">Hot Games</Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink className='cursor-pointer text-white hover:text-purple-400 transition-colors px-3 py-2 rounded-md hover:bg-gray-800' asChild>
                    <Link href="/new">New Games</Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Right side - Search and mobile search toggle */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Desktop search */}
            <div className="hidden md:block">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="search"
                  placeholder="Search games..."
                  className="bg-gray-800 text-white p-2.5 pl-10 rounded-full w-[280px] lg:w-[350px] focus:outline-none focus:ring-2 focus:ring-purple-500 focus:bg-gray-700 transition-all border border-gray-700 hover:border-gray-600"
                />
              </div>
            </div>

            {/* Mobile search toggle */}
            <button
              onClick={() => setIsMobileSearchOpen(!isMobileSearchOpen)}
              className="md:hidden p-2 hover:bg-gray-800 rounded-lg transition-colors"
              aria-label={isMobileSearchOpen ? "Close search" : "Open search"}
            >
              {isMobileSearchOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Search className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile search bar */}
        {isMobileSearchOpen && (
          <div className="md:hidden mt-3 pb-2 animate-in slide-in-from-top-2 duration-200">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="search"
                placeholder="Search games..."
                className="w-full bg-gray-800 text-white p-3 pl-10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:bg-gray-700 transition-all border border-gray-700"
                autoFocus
              />
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
