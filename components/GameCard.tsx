
import Link from 'next/link';
import Image from 'next/image';

interface GameCardProps {
  game: {
    id: number;
    name: string;
    coverImageUrl: string | null;
    slug: string;
  };
}

export function GameCard({ game }: GameCardProps) {
  return (
    <Link title={game.name} href={`/game/${game.slug}`} className="block w-[180px] h-[130px] p-0 m-0 bg-gray-800 overflow-hidden hover:scale-101 rounded-lg">
      <div className="relative w-full h-full">
        {game.coverImageUrl ? (
          <Image
            src={game.coverImageUrl}
            alt={game.name}
            layout="fill"
            objectFit="cover"
            className=""
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gray-700">
            <span className="text-white text-center text-sm p-2">{game.name}</span>
          </div>
        )}
      </div>
    </Link>
  );
}
