/* 导航栏移动端优化样式 */

/* 确保导航栏在所有设备上都有合适的高度 */
.navbar-container {
  min-height: 64px;
}

/* 移动端搜索框动画 */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-search-enter {
  animation: slideInFromTop 0.2s ease-out;
}

/* 移动端菜单按钮悬停效果 */
.mobile-menu-button {
  transition: all 0.2s ease;
}

.mobile-menu-button:hover {
  background-color: rgba(55, 65, 81, 0.5);
  transform: scale(1.05);
}

.mobile-menu-button:active {
  transform: scale(0.95);
}

/* 桌面端导航链接悬停效果 */
.nav-link {
  position: relative;
  transition: all 0.2s ease;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #a855f7, #ec4899);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover::after {
  width: 100%;
}

/* 搜索框聚焦效果 */
.search-input {
  transition: all 0.3s ease;
}

.search-input:focus {
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
  border-color: #a855f7;
}

/* 移动端侧边栏优化 */
.mobile-sidebar {
  backdrop-filter: blur(10px);
}

.mobile-sidebar-content {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
}

/* 响应式字体大小 */
@media (max-width: 640px) {
  .navbar-logo {
    font-size: 1.125rem; /* 18px */
  }
}

@media (min-width: 641px) {
  .navbar-logo {
    font-size: 1.25rem; /* 20px */
  }
}

@media (min-width: 768px) {
  .navbar-logo {
    font-size: 1.5rem; /* 24px */
  }
}

/* 确保触摸目标足够大 */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* 防止文本选择 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 平滑滚动 */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .navbar-container {
    border-bottom: 2px solid #ffffff;
  }
  
  .nav-link {
    font-weight: 600;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .navbar-container {
    background-color: #000000;
    border-bottom-color: #374151;
  }
}
