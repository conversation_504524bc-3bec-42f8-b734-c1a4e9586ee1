import { getCategories } from "@/lib/data";
import Link from "next/link";

export async function Footer() {
  const categories = await getCategories();

  return (
    <footer className="text-white py-8 px-4 sm:px-6 lg:px-8 border border-top-1 border-gray-700">
      <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-1">
          <h3 className="text-lg font-semibold mb-4">Game Categories</h3>
          <ul className="space-y-2">
            {categories.map((category) => (
              <li key={category.id}>
                <Link href={`/category/${category.id}`} className="hover:text-gray-300 transition-colors duration-200">
                  {category.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>
        <div className="md:col-span-1">
          <h3 className="text-lg font-semibold mb-4">About</h3>
          <ul className="space-y-2">
            <li>
              <Link href="/about" className="hover:text-gray-300 transition-colors duration-200">
                About Us
              </Link>
            </li>
            <li>
              <Link href="/contact" className="hover:text-gray-300 transition-colors duration-200">
                Contact Us
              </Link>
            </li>
          </ul>
        </div>
        <div className="md:col-span-1">
          <h3 className="text-lg font-semibold mb-4">Legal</h3>
          <ul className="space-y-2">
            <li>
              <Link href="/terms" className="hover:text-gray-300 transition-colors duration-200">
                Terms of Service
              </Link>
            </li>
            <li>
              <Link href="/privacy" className="hover:text-gray-300 transition-colors duration-200">
                Privacy Policy
              </Link>
            </li>
          </ul>
        </div>
      </div>
      <div className="mt-8 border-t border-gray-700 pt-4 text-center text-sm">
        <p>&copy; {new Date().getFullYear()} lovetestergame.com. All rights reserved.</p>
      </div>
    </footer>
  );
}