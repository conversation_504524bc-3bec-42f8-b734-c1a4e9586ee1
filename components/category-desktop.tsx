'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Gamepad2, X, Car } from 'lucide-react';
import { getCategories } from '@/lib/data';
import { categories } from '@/lib/categories';


interface CategorySheetProps {
  isOpen: boolean;
}

export function CategoryDesktop({isOpen }: CategorySheetProps) {
  const pathname = usePathname();

  return (
    <>
      {/* Backdrop */}
     
      {/* Sheet */}
      <div
        className={`hidden lg:block pl-4 pr-4  text-white transform transition-transform duration-300 ease-out ${isOpen ? 'w-[250px] ': 'w-[40x] border-r border-gray-700'}`}
      >
        {/* Header */}
        {/* Content */}
        <div className="overflow-y-auto h-full">
          <nav>
            <ul className="space-y-2">
              {/* Home link */}
              <li>
                <Link
                  href="/"
                  className={`flex items-center rounded-lg hover:bg-gray-700 transition-colors ${
                    pathname === '/' ? 'hover:bg-purple-700' : ''
                  }`}
                > 
                  <Gamepad2 className="w-[24px] h-[24px] px-3" />
                  <span className={`font-medium ${isOpen ? 'ml-3' : 'hidden'}`}>All Games</span>
                </Link>
              </li>

              {/* Category links */}
              {categories.map((category) => {
                const isActive = pathname === `/category/${category?.slug}`;
                return (
                  <li key={category.slug}>
                    <Link
                      href={`/category/${category.slug}`}
                      className={`flex items-center p-3 group rounded-lg hover:bg-gray-700 transition-colors ${
                        isActive ? 'bg-purple-600 hover:bg-purple-700' : ''
                      }`}
                    >
                      <Car className='w-[24px] h-[24px]'/>
                      <span className={`font-medium group-hover:ml-3 transition-all duration-300 ${isOpen ? 'ml-3' : 'hidden'}`}>{category.name}</span>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </nav>
        </div>
      </div>
    </>
  );
}
