
import { pgTable, serial, varchar, integer, timestamp, text } from 'drizzle-orm/pg-core';

export const categories = pgTable('categories', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 50 }).unique().notNull(),
  sortOrder: integer('sort_order').default(0).notNull(),
  slug: varchar('slug', { length: 200 }).unique().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export const games = pgTable('games', {
  id: serial('id').primaryKey(),
  slug: varchar('slug', { length: 200 }).unique().notNull(),
  categoryId: varchar('category_id').references(() => categories.slug).notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  source: varchar('source', { length: 100 }),
  coverImageUrl: varchar('cover_image_url', { length: 255 }),
  gameUrl: varchar('game_url', { length: 255 }).notNull(),
  status: integer('status').default(1).notNull(),
  playCount: integer('play_count').default(0).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});
