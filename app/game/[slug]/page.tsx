
import { getGame } from '@/lib/data';

export default async function GamePage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  console.log(slug)
  const game = await getGame(slug);

  if (!game) {
    return <div className="text-center text-xl">Game not found</div>;
  }

  return (
    <div className="max-w-5xl min-h-screen">
      <div className="p-4 md:p-6">
        
        <div className="aspect-w-16 aspect-h-9 rounded-lg w-full h-full lg:w-[1000px] lg:h-[600px]  overflow-hidden">
          <iframe src={game.gameUrl} className="w-full h-full border-0" allowFullScreen />
        </div>
        <div className='flex flex-row  items-end gap-4 mt-4'>
          <h1 className="text-3xl font-bold text-purple-400">{game.name}</h1>
          {game.source && <p className="text-sm text-gray-400">Source: {game.source}</p>}
         </div>
      </div>
    </div>
  );
}
