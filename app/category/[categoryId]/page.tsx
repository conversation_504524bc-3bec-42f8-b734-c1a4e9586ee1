
import { getGamesByCategory, getCategory } from '@/lib/data';
import { GameCard } from '@/components/GameCard';

export default async function CategoryPage({ params }: { params: Promise<{ slug: string }> }) {
  const slug = (await params).slug;
  const games = await getGamesByCategory(slug);
  const category = await getCategory(slug);

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-purple-400">{category?.name}</h1>
      <div className="flex flex-wrap gap-3">
        {games.map((game) => (
          <GameCard key={game.id} game={game} />
        ))}
      </div>
    </div>
  );
}
