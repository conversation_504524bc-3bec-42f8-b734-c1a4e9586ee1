
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AppLayout } from "@/components/AppLayout";
import { getCategories } from "@/lib/data";
import { Footer } from "@/components/Footer";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Love Tester - Discover Your Compatibility with Your Crush | Fun Love Test Game",
  description: "Play Love Tester and find out if you and your crush are meant to be together! Enter names, ages, heights, and eye colors to test your love compatibility. Choose between simple or advanced gameplay. A fun and romantic online game that’s perfect for a quick heart match!",
  keywords: "love tester, love test game, crush compatibility, love calculator, relationship test, dating game, romantic games, fun love games, online love test, love match"
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const categories = await getCategories();

  return (
    <html lang="en">
      <body className={`${inter.className} bg-black text-white`}>
        <div className="flex flex-col min-h-screen">
          <AppLayout categories={categories}>
            <main className="flex-grow">{children}</main>
          </AppLayout>
          <Footer />
        </div>
      </body>
    </html>
  );
}

