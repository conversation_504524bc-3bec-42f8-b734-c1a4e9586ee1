import { getGames, getRelatedGame, getPopluarGame } from '@/lib/data';
import { GameCard } from '@/components/GameCard';
import { Star } from 'lucide-react';
import Link from 'next/link';

export default async function Home() {
  const games = await getGames();

  const RelatedGame = await getRelatedGame();
  const popluarGame = await getPopluarGame();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Hero Section */}
        <div className="mb-12">
          {/* Game Title and Rating */}
          <div className="mb-8">
            <div className="text-center mb-6">
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent mb-4">
                Pirate Love Tester
              </h1>
              <p className="text-gray-300 text-lg max-w-2xl mx-auto">
                Discover your romantic compatibility with this fun and magical love testing adventure!
              </p>
            </div>

            <div className="flex items-center justify-center gap-6 mb-6">
              <div className="flex items-center gap-2 bg-yellow-500/20 px-4 py-2 rounded-full">
                <Star className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                <span className="text-lg font-semibold text-yellow-400">9.4</span>
                <span className="text-sm text-gray-400">/ 10</span>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">1,234</div>
                <div className="text-sm text-gray-400">Players</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">Free</div>
                <div className="text-sm text-gray-400">To Play</div>
              </div>
            </div>
          </div>

          {/* Main Game Layout */}
          <div className="max-w-7xl mx-auto">
            {/* Game Frame */}
            <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl overflow-hidden shadow-2xl border border-gray-700">
              <div className="bg-gray-800 px-6 py-4 border-b border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <span className="text-gray-300 text-sm font-medium">Pirate Love Tester</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-gray-400">
                    <span>🔒 Secure</span>
                    <span>•</span>
                    <span>📱 Mobile Friendly</span>
                  </div>
                </div>
              </div>

              {/* Desktop Game */}
              <div className="hidden md:block w-full h-[600px]">
                <iframe
                  src="https://bitent.com/html5/pirate_love_tester/?key=y8&value=default"
                  className="w-full h-full"
                  title="Pirate Love Tester Game"
                />
              </div>
              
            </div>
          </div>
        </div>
        {/* Game Description */}
        <section className="mb-16">
          <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl p-8 border border-gray-700">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">About Pirate Love Tester</h2>
              <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto rounded-full"></div>
            </div>

            <div className="max-w-4xl mx-auto">
              <p className="text-gray-300 text-lg leading-relaxed mb-8 text-center">
                Set sail on a journey of love and adventure with Love Tester, a lighthearted game that reveals how well you and your crush match up!
                Enter basic details like names, ages, heights, and eye colors, then let the system analyze your compatibility using its magical algorithm.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-xl p-6 border border-purple-500/30">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-2xl">🚀</span>
                    </div>
                    <h3 className="text-purple-300 font-semibold text-xl">Simple Mode</h3>
                  </div>
                  <p className="text-gray-300">Get instant results with a quick match! Perfect for those who want immediate answers about their romantic compatibility.</p>
                </div>

                <div className="bg-gradient-to-br from-pink-900/30 to-pink-800/20 rounded-xl p-6 border border-pink-500/30">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                      <span className="text-2xl">⭐</span>
                    </div>
                    <h3 className="text-pink-300 font-semibold text-xl">Advanced Mode</h3>
                  </div>
                  <p className="text-gray-300">Dive deeper with more inputs and see if the stars align for your perfect match. More detailed analysis awaits!</p>
                </div>
              </div>

              <div className="text-center mb-8">
                <p className="text-gray-300 text-lg leading-relaxed">
                  Whether you're curious, crushing, or just having fun with friends, Love Tester is the perfect game to bring a spark of romance and a smile to your day.
                </p>
              </div>

              <div className="bg-gradient-to-r from-purple-900/40 via-pink-900/40 to-purple-900/40 rounded-xl p-6 border border-purple-500/30 text-center">
                <p className="text-purple-200 font-medium text-lg mb-4">
                  💕 Are you and your crush a perfect match? 💕
                </p>
                <p className="text-gray-300 mb-4">
                  There's only one way to find out — play now and discover your romantic destiny!
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <span className="bg-purple-600/30 text-purple-300 px-4 py-2 rounded-full text-sm">❤️ Fun & Free</span>
                  <span className="bg-pink-600/30 text-pink-300 px-4 py-2 rounded-full text-sm">🎮 Easy to Play</span>
                  <span className="bg-purple-600/30 text-purple-300 px-4 py-2 rounded-full text-sm">✨ Magical Results</span>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* Related Games */}
        <section className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">Related Love Games</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Discover more romantic adventures and compatibility tests that will spark your curiosity
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mt-4 rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {RelatedGame.slice(0, 8).map((game) => (
              <div key={game.id} className="group">
                <GameCard game={game} />
              </div>
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/category/love-games"
              className="inline-flex items-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              View All Love Games
              <span className="text-lg">→</span>
            </Link>
          </div>
        </section>

        {/* Popular Games */}
        <section className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">Most Popular Games</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Join thousands of players enjoying these trending games right now
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-500 mx-auto mt-4 rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 xl:grid-cols-6 gap-5">
            {popluarGame.slice(0, 8).map((game) => (
              <div key={`popular-${game.id}`} className="group">
                <GameCard game={game} />
              </div>
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/popular"
              className="inline-flex items-center gap-2 bg-pink-600 hover:bg-pink-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              View All Popular Games
              <span className="text-lg">→</span>
            </Link>
          </div>
        </section>
      </div>
    </div>
  );
}

