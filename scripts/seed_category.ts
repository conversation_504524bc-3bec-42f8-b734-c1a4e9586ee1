import { db } from '@/lib/db';
import { categories, games } from '@/db/schema';

async function main() {
  // Seed categories
  const categoryList = [
    { name: 'Driving', slug: 'driving', sortOrder: 1 },
    { name: 'Shooting',slug: 'shooting', sortOrder: 2 },
    { name: 'For Girls', slug: 'for-girls', sortOrder: 3 },
    { name: 'Thinking',slug: 'thinking', sortOrder: 4 },
    { name: 'Fighting', slug: 'fighting', sortOrder: 5 },
    { name: 'ManageMent & Slm', slug: 'management-slm', sortOrder: 6 },
    { name: 'Sports',slug: 'sports', sortOrder: 7 },
    { name: 'Skill', slug: 'skill', sortOrder: 8 },
    { name: 'Strategy & RPG', slug: 'strategy-rpg', sortOrder: 9 },
    { name: 'Arcade & Classic', sortOrder: 10 },
    { name: 'Action & Adventure',slug: 'action-adventure', sortOrder: 11 },
    { name: 'Fun & Crazy', slug: 'fun-crazy', sortOrder: 12 },
  
  ].map(c => ({
    ...c,
    slug: c.name.replace(/\s+/g, '-').toLowerCase(),
  }));
  const insertedCategories = await db.insert(categories).values(categoryList).returning();

  // Helper: get category id by name
  const getCategoryId = (name: string) => insertedCategories.find(c => c.name === name)?.slug;

  // Seed games, only if categoryId is found, and ensure type is correct
  console.log(getCategoryId);
  const gameList = Array.from({ length: 20 }).map((_, i) => {
    const categoryName = categoryList[i % categoryList.length].name;
    return {
      name: `Test Game ${i + 1}`,
      slug: `test-game-${i + 1}`,
      description: `This is a description for Test Game ${i + 1}.`,
      gameUrl: `https://example.com/game-${i + 1}`,
      categoryId: getCategoryId(categoryName) || '',
      createdAt: new Date(),
      updatedAt: new Date(),
      // Add other fields as required by your games schema
    };
  }).filter(game => game.categoryId);

  await db.insert(games).values(gameList);
  console.log('Seeded 20 test games.');
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});
