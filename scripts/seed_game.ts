import { db } from '@/lib/db';
import { games } from '@/db/schema';

type Game = typeof games.$inferInsert;

async function main() {
  // Seed game
  const gameList: Game[] = Array.from({ length: 20 }).map((_, i) => {
    return {
      name: `Test Game ${i + 1}`,
      slug: `test-game-${i + 1}`,
      description: `This is a description for Test Game ${i + 1}.`,
      source: i % 2 === 0 ? 'Open Source' : 'In-house',
      coverImageUrl: `https://placehold.co/400x400?text=Game+${i + 1}`,
      gameUrl: `https://example.com/game-${i + 1}`,
      categoryId: 38, // 请根据实际分类ID调整
      status: 1,
      playCount: Math.floor(Math.random() * 1000),
      createdAt: new Date(),
      updatedAt: new Date(),
      // Add other fields as required by your games schema
    };
  });

  await db.insert(games).values(gameList);
  console.log('Seeded 20 test games.');
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});
