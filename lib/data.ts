
import { db } from './db';
import { categories, games } from '@/db/schema';
import { eq, like } from 'drizzle-orm';

export async function getCategories() {
  return await db.query.categories.findMany({
    orderBy: (categories, { asc }) => [asc(categories.sortOrder)],
  });
}

export async function getGames() {
  return await db.query.games.findMany();
}

export async function getGamesByCategory(categoryId: string) {
  return await db.query.games.findMany({
    where: eq(games.categoryId, categoryId),
  });
}

export async function getGame(slug: string) {
  return await db.query.games.findFirst({
    where: eq(games.slug, slug),
  });
}

export async function getCategory(slug: string) {
    return await db.query.categories.findFirst({
        where: eq(categories.slug, slug),
    });
}

export async function getRelatedGame() {
    return await db.query.games.findMany({
        where: like(games.name, '%love%'),
        limit: 10,
    });
}

export async function getPopluarGame() {
    return await db.query.games.findMany({
        limit:10
    });
}
